const express = require('express');
const { handleDiscordCallback, getCurrentUser } = require('../controllers/authController');
const { protect } = require('../middleware/auth');
const { authValidation } = require('../middleware/validation');
const { authLimiter } = require('../middleware/security');

const router = express.Router();

// POST /api/auth/discord - Handle Discord OAuth callback
router.post('/discord', authLimiter, authValidation.discordCallback, handleDiscordCallback);

// GET /api/auth/me - Get current user profile
router.get('/me', protect, getCurrentUser);

module.exports = router;
