const request = require('supertest');
const app = require('../../src/app');
const User = require('../../src/models/User');

// Mock Discord API calls
jest.mock('axios');
const axios = require('axios');

describe('Auth API Integration Tests', () => {
  describe('POST /api/auth/discord/callback', () => {
    beforeEach(() => {
      // Reset mocks before each test
      jest.clearAllMocks();
    });

    test('should authenticate user with valid Discord code', async () => {
      // Mock Discord token exchange
      axios.post.mockResolvedValueOnce({
        data: {
          access_token: 'mock_access_token',
          token_type: 'Bearer',
          expires_in: 3600
        }
      });

      // Mock Discord user data
      axios.get.mockResolvedValueOnce({
        data: {
          id: '123456789',
          username: 'testuser',
          email: '<EMAIL>',
          avatar: 'avatar_hash'
        }
      });

      const response = await request(app)
        .post('/api/auth/discord/callback')
        .send({ code: 'valid_discord_code' })
        .expect(200);

      expect(response.body).toHaveProperty('token');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user.discordId).toBe('123456789');
      expect(response.body.user.username).toBe('testuser');

      // Verify user was created in database
      const user = await User.findOne({ discordId: '123456789' });
      expect(user).toBeDefined();
    });

    test('should return 400 for missing code', async () => {
      const response = await request(app)
        .post('/api/auth/discord/callback')
        .send({})
        .expect(400);

      expect(response.body).toHaveProperty('error');
    });

    test('should return 401 for invalid Discord code', async () => {
      axios.post.mockRejectedValueOnce({
        response: { status: 400, data: { error: 'invalid_grant' } }
      });

      const response = await request(app)
        .post('/api/auth/discord/callback')
        .send({ code: 'invalid_code' })
        .expect(401);

      expect(response.body).toHaveProperty('error');
    });

    test('should update existing user on subsequent logins', async () => {
      // Create existing user
      const existingUser = new User({
        discordId: '123456789',
        username: 'oldusername',
        email: '<EMAIL>',
        displayName: 'Old Name',
        avatar: 'old_avatar'
      });
      await existingUser.save();

      // Mock Discord responses with updated data
      axios.post.mockResolvedValueOnce({
        data: { access_token: 'mock_access_token' }
      });

      axios.get.mockResolvedValueOnce({
        data: {
          id: '123456789',
          username: 'newusername',
          email: '<EMAIL>',
          avatar: 'new_avatar_hash'
        }
      });

      const response = await request(app)
        .post('/api/auth/discord/callback')
        .send({ code: 'valid_code' })
        .expect(200);

      expect(response.body.user.username).toBe('newusername');
      expect(response.body.user.email).toBe('<EMAIL>');

      // Verify user was updated, not duplicated
      const userCount = await User.countDocuments({ discordId: '123456789' });
      expect(userCount).toBe(1);
    });
  });

  describe('GET /api/auth/me', () => {
    let authToken;
    let testUser;

    beforeEach(async () => {
      // Create test user and get auth token
      testUser = new User(global.testUtils.createTestUser());
      await testUser.save();

      const authService = require('../../src/services/authService');
      authToken = authService.generateJWT(testUser);
    });

    test('should return user data for authenticated request', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.discordId).toBe(testUser.discordId);
      expect(response.body.username).toBe(testUser.username);
      expect(response.body.email).toBe(testUser.email);
    });

    test('should return 401 for missing token', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .expect(401);

      expect(response.body).toHaveProperty('error');
    });

    test('should return 401 for invalid token', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', 'Bearer invalid_token')
        .expect(401);

      expect(response.body).toHaveProperty('error');
    });

    test('should return 401 for malformed authorization header', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', 'InvalidFormat')
        .expect(401);

      expect(response.body).toHaveProperty('error');
    });
  });

  describe('POST /api/auth/logout', () => {
    let authToken;
    let testUser;

    beforeEach(async () => {
      testUser = new User(global.testUtils.createTestUser());
      await testUser.save();

      const authService = require('../../src/services/authService');
      authToken = authService.generateJWT(testUser);
    });

    test('should successfully logout authenticated user', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('message');
    });

    test('should return 401 for unauthenticated logout attempt', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .expect(401);

      expect(response.body).toHaveProperty('error');
    });
  });
});
