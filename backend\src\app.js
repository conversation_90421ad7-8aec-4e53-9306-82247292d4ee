require('dotenv').config();
const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const path = require('path');
const fs = require('fs');
const routes = require('./routes');
const logger = require('./config/logger');
const {
  helmetConfig,
  mongoSanitizeConfig,
  sanitizeInput,
  securityLogger,
  generalLimiter
} = require('./middleware/security');
const {
  globalErrorHandler,
  notFound
} = require('./middleware/errorHandler');

// Create Express app
const app = express();

// Create uploads directory if it doesn't exist (only in non-test environment)
if (process.env.NODE_ENV !== 'test') {
  const uploadsDir = path.join(process.cwd(), process.env.UPLOAD_DIR || 'uploads');
  if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
  }

  // Create logs directory if it doesn't exist
  const logsDir = path.join(process.cwd(), 'logs');
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
  }

  // Serve static files from uploads directory
  app.use('/uploads', express.static(uploadsDir));
}

// Security middleware
app.use(helmetConfig);
app.use(mongoSanitizeConfig);
app.use(generalLimiter);
app.use(securityLogger);
app.use(sanitizeInput);

// Basic middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Only use morgan in non-test environment
if (process.env.NODE_ENV !== 'test') {
  app.use(morgan('dev'));
}

// API routes
app.use('/api', routes);

// 404 handler - must come before global error handler
app.use(notFound);

// Global error handling middleware
app.use(globalErrorHandler);

module.exports = app;
