const express = require('express');
const authRoutes = require('./authRoutes');
const userRoutes = require('./userRoutes');
const contentRoutes = require('./contentRoutes');
const uploadRoutes = require('./uploadRoutes');
const chatRoutes = require('./chatRoutes');
const notificationRoutes = require('./notificationRoutes');
const mentorRoutes = require('./mentorRoutes');
const discordBotRoutes = require('./discordBotRoutes');
const errorRoutes = require('./errorRoutes');

const router = express.Router();

// Health check route
router.get('/health', (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'API is running'
  });
});

// Mount routes
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/content', contentRoutes);
router.use('/upload', uploadRoutes);
router.use('/chat', chatRoutes);
router.use('/notifications', notificationRoutes);
router.use('/mentors', mentorRoutes);
router.use('/discord-bot', discordBotRoutes);
router.use('/errors', errorRoutes);

module.exports = router;
