import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { User } from '../models/user.model';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();
  private tokenKey = 'discord_token';

  constructor(
    private http: HttpClient,
    private router: Router,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.checkAuthStatus();
  }

  private isBrowser(): boolean {
    return isPlatformBrowser(this.platformId);
  }

  /**
   * Check if the user is already authenticated
   */
  private checkAuthStatus(): void {
    if (!this.isBrowser()) return;
    const token = localStorage.getItem(this.tokenKey);
    if (token) {
      this.getUserProfile().subscribe();
    }
  }

  /**
   * Get the Discord OAuth URL for login
   */
  getDiscordAuthUrl(): string {
    const clientId = environment.discord.clientId;
    const redirectUri = encodeURIComponent(environment.discord.redirectUri);
    const scope = encodeURIComponent('identify email');
    return `${environment.discord.apiEndpoint}/oauth2/authorize?client_id=${clientId}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}`;
  }

  /**
   * Handle the OAuth callback from Discord
   * @param code The authorization code from Discord
   */
  handleAuthCallback(code: string): Observable<User> {
    return this.http.post<any>(`${environment.apiUrl}/auth/discord`, { code })
      .pipe(
        tap(response => {
          if (response.success && response.token) {
            if (this.isBrowser()) {
              localStorage.setItem(this.tokenKey, response.token);
            }
            this.isAuthenticatedSubject.next(true);
            if (response.user) {
              this.currentUserSubject.next(response.user);
            }
          } else {
            throw new Error(response.message || 'Authentication failed');
          }
        }),
        map(response => response.user),
        catchError(error => {
          console.error('Authentication error:', error);
          return throwError(() => new Error(error.error?.message || 'Authentication failed'));
        })
      );
  }

  /**
   * Get the current user's profile
   */
  getUserProfile(): Observable<User> {
    if (!this.isBrowser()) {
      this.isAuthenticatedSubject.next(false);
      return throwError(() => new Error('Not authenticated'));
    }
    const token = localStorage.getItem(this.tokenKey);
    if (!token) {
      this.isAuthenticatedSubject.next(false);
      return throwError(() => new Error('Not authenticated'));
    }

    return this.http.get<any>(`${environment.apiUrl}/auth/me`, {
      headers: { Authorization: `Bearer ${token}` }
    }).pipe(
      tap(response => {
        if (response.success && response.user) {
          this.currentUserSubject.next(response.user);
          this.isAuthenticatedSubject.next(true);
        } else {
          throw new Error(response.message || 'Failed to fetch user profile');
        }
      }),
      map(response => response.user),
      catchError(error => {
        console.error('Error fetching user profile:', error);
        if (error.status === 401) {
          this.logout();
        }
        return throwError(() => new Error(error.error?.message || 'Failed to fetch user profile'));
      })
    );
  }

  /**
   * Log the user out
   */
  logout(): void {
    if (this.isBrowser()) {
      localStorage.removeItem(this.tokenKey);
    }
    this.currentUserSubject.next(null);
    this.isAuthenticatedSubject.next(false);
    this.router.navigate(['/']);
  }

  /**
   * Get the current user
   */
  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  /**
   * Check if the user is authenticated
   */
  isAuthenticated(): boolean {
    return this.isAuthenticatedSubject.value;
  }

  /**
   * Get the current auth token
   */
  getToken(): string | null {
    if (!this.isBrowser()) return null;
    return localStorage.getItem(this.tokenKey);
  }

  /**
   * Update the current user in the subject
   */
  updateCurrentUser(user: User): void {
    this.currentUserSubject.next(user);
  }
}
